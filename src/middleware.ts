import { NextRequest, NextResponse } from 'next/server';

import { validateAccessToken } from '@/lib/auth';

/**
 * Middleware to protect admin routes
 * Checks for valid access_token in cookies and redirects to signin if invalid/expired
 */
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Only run middleware for admin-panel routes
  if (pathname.startsWith('/')) {
    try {
      // Get access token from cookies
      const accessToken = request.cookies.get('access_token')?.value;

      // Log for debugging (remove in production)
      console.log(`[Middleware] Checking access to: ${pathname}`);
      console.log(`[Middleware] Token present: ${!!accessToken}`);

      // Validate the token
      const isValidToken = validateAccessToken(accessToken);

      if (!isValidToken) {
        // Token is missing, invalid, or expired - redirect to signin
        console.log(`[Middleware] Invalid token, redirecting to signin`);

        const signinUrl = new URL('/login', request.url);

        // Add the original URL as a redirect parameter so user can be redirected back after login
        signinUrl.searchParams.set('redirect', pathname);

        return NextResponse.redirect(signinUrl);
      }

      // Token is valid, allow the request to continue
      console.log(`[Middleware] Valid token, allowing access`);
      return NextResponse.next();
    } catch (error) {
      // If there's any error in middleware, redirect to signin for safety
      console.error(`[Middleware] Error processing request:`, error);

      const signinUrl = new URL('/login', request.url);
      signinUrl.searchParams.set('redirect', pathname);

      return NextResponse.redirect(signinUrl);
    }
  }

  // For non-admin routes, continue without authentication check
  return NextResponse.next();
}

/**
 * Configure which routes the middleware should run on
 */
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (images, etc.)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
